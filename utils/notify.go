package utils

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log/slog"
	"net/http"
	"strings"
	"time"

	"github.com/beck-8/subs-check/config"
)

// NotifyRequest 定义发送通知的请求结构
type NotifyRequest struct {
	URLs  string `json:"urls"`  // 通知目标的 URL（如 mailto://、discord://）
	Body  string `json:"body"`  // 通知内容
	Title string `json:"title"` // 通知标题（可选）
}

// NotifyStats 通知统计信息
type NotifyStats struct {
	TotalNodes     int           // 总节点数
	AvailableNodes int           // 可用节点数
	SuccessRate    float64       // 成功率
	AvgSpeed       float64       // 平均速度 (MB/s)
	Duration       time.Duration // 检测耗时
	StartTime      time.Time     // 检测开始时间
	// 流媒体解锁统计
	NetflixCount  int // Netflix解锁节点数
	YouTubeCount  int // YouTube解锁节点数
	OpenAICount   int // OpenAI解锁节点数
	DisneyCount   int // Disney+解锁节点数
	GeminiCount   int // Gemini解锁节点数
	TikTokCount   int // TikTok解锁节点数
}

// Notify 发送通知
func Notify(request NotifyRequest) error {
	// 构建请求体
	body, err := json.Marshal(request)
	if err != nil {
		return fmt.Errorf("构建请求体失败: %w", err)
	}

	// 发送请求
	resp, err := http.Post(config.GlobalConfig.AppriseApiServer, "application/json", bytes.NewBuffer(body))
	if err != nil {
		return fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 检查响应状态码
	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("通知失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	return nil
}

func SendNotify(length int) {
	if config.GlobalConfig.AppriseApiServer == "" {
		return
	} else if len(config.GlobalConfig.RecipientUrl) == 0 {
		slog.Error("没有配置通知目标")
		return
	}

	for _, url := range config.GlobalConfig.RecipientUrl {
		request := NotifyRequest{
			URLs: url,
			Body: fmt.Sprintf("✅ 可用节点：%d\n🕒 %s",
				length,
				GetCurrentTime()),
			Title: config.GlobalConfig.NotifyTitle,
		}
		var err error
		for i := 0; i < config.GlobalConfig.SubUrlsReTry; i++ {
			err = Notify(request)
			if err == nil {
				slog.Info(fmt.Sprintf("%s 通知发送成功", strings.SplitN(url, "://", 2)[0]))
				break
			}
		}
		if err != nil {
			slog.Error(fmt.Sprintf("%s 发送通知失败: %v", strings.SplitN(url, "://", 2)[0], err))
		}
	}
}

// SendEnhancedNotify 发送增强的通知
func SendEnhancedNotify(stats NotifyStats) {
	if config.GlobalConfig.AppriseApiServer == "" {
		return
	} else if len(config.GlobalConfig.RecipientUrl) == 0 {
		slog.Error("没有配置通知目标")
		return
	}

	body := generateNotifyBody(stats)

	for _, url := range config.GlobalConfig.RecipientUrl {
		request := NotifyRequest{
			URLs:  url,
			Body:  body,
			Title: config.GlobalConfig.NotifyTitle,
		}
		var err error
		for i := 0; i < config.GlobalConfig.SubUrlsReTry; i++ {
			err = Notify(request)
			if err == nil {
				slog.Info(fmt.Sprintf("%s 增强通知发送成功", strings.SplitN(url, "://", 2)[0]))
				break
			}
		}
		if err != nil {
			slog.Error(fmt.Sprintf("%s 发送增强通知失败: %v", strings.SplitN(url, "://", 2)[0], err))
		}
	}
}

func GetCurrentTime() string {
	return time.Now().Format("2006-01-02 15:04:05")
}

// 移除calculateMediaStats函数 - 数据处理移到app.go中

// 移除calculateAvgSpeed函数 - 数据处理移到app.go中

// formatDuration 格式化持续时间
func formatDuration(d time.Duration) string {
	if d < time.Minute {
		return fmt.Sprintf("%d秒", int(d.Seconds()))
	}
	minutes := int(d.Minutes())
	seconds := int(d.Seconds()) % 60
	return fmt.Sprintf("%d分%d秒", minutes, seconds)
}

// generateNotifyBody 生成增强的通知内容
func generateNotifyBody(stats NotifyStats) string {
	successRate := 0.0
	if stats.TotalNodes > 0 {
		successRate = float64(stats.AvailableNodes) / float64(stats.TotalNodes) * 100
	}

	// 构建通知内容
	body := "🚀 **节点检测完成**\n\n"

	// 检测统计
	body += "📊 **检测统计**\n"
	body += fmt.Sprintf("• 总节点数：%d 个\n", stats.TotalNodes)
	body += fmt.Sprintf("• 可用节点：%d 个\n", stats.AvailableNodes)
	body += fmt.Sprintf("• 成功率：%.1f%%\n", successRate)
	if stats.AvgSpeed > 0 {
		body += fmt.Sprintf("• 平均速度：%.1f MB/s\n", stats.AvgSpeed)
	}
	body += fmt.Sprintf("• 检测耗时：%s\n\n", formatDuration(stats.Duration))

	// 流媒体解锁统计
	hasMedia := stats.NetflixCount > 0 || stats.YouTubeCount > 0 || stats.OpenAICount > 0 ||
	           stats.DisneyCount > 0 || stats.GeminiCount > 0 || stats.TikTokCount > 0
	if hasMedia {
		body += "🎬 **流媒体解锁**\n"
		if stats.NetflixCount > 0 {
			body += fmt.Sprintf("• Netflix：%d 个节点\n", stats.NetflixCount)
		}
		if stats.YouTubeCount > 0 {
			body += fmt.Sprintf("• YouTube：%d 个节点\n", stats.YouTubeCount)
		}
		if stats.OpenAICount > 0 {
			body += fmt.Sprintf("• OpenAI：%d 个节点\n", stats.OpenAICount)
		}
		if stats.DisneyCount > 0 {
			body += fmt.Sprintf("• Disney+：%d 个节点\n", stats.DisneyCount)
		}
		if stats.GeminiCount > 0 {
			body += fmt.Sprintf("• Gemini：%d 个节点\n", stats.GeminiCount)
		}
		if stats.TikTokCount > 0 {
			body += fmt.Sprintf("• TikTok：%d 个节点\n", stats.TikTokCount)
		}
		body += "\n"
	}

	// 订阅链接
	domain := "subs-check.dearmer.live"
	body += "📋 **订阅链接 (长按复制)**\n\n"
	body += "📱 Clash/Mihomo 配置\n"
	body += fmt.Sprintf("https://%s/sub/all.yaml\n\n", domain)
	body += "🌐 V2Ray/Base64 格式\n"
	body += fmt.Sprintf("https://%s/sub/base64.txt\n\n", domain)
	body += "⚙️ 带规则配置文件\n"
	body += fmt.Sprintf("https://%s/sub/mihomo.yaml\n\n", domain)

	// 检测时间
	body += fmt.Sprintf("⏰ 检测时间：%s", stats.StartTime.Format("2006-01-02 15:04:05"))

	return body
}
