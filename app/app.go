package app

import (
	"flag"
	"fmt"
	"log/slog"
	"os"
	"path/filepath"
	"runtime"
	"runtime/debug"
	"strings"
	"sync/atomic"
	"time"

	"github.com/beck-8/subs-check/app/monitor"
	"github.com/beck-8/subs-check/assets"
	"github.com/beck-8/subs-check/check"
	"github.com/beck-8/subs-check/config"
	"github.com/beck-8/subs-check/save"
	"github.com/beck-8/subs-check/utils"
	"github.com/fsnotify/fsnotify"
	"github.com/robfig/cron/v3"
)

// App 结构体用于管理应用程序状态
type App struct {
	configPath string
	interval   int
	watcher    *fsnotify.Watcher
	checkChan  chan struct{} // 触发检测的通道
	checking   atomic.Bool   // 检测状态标志
	ticker     *time.Ticker
	done       chan struct{} // 用于结束ticker goroutine的信号
	cron       *cron.Cron    // crontab调度器
	version    string
}

// New 创建新的应用实例
func New(version string) *App {
	configPath := flag.String("f", "", "配置文件路径")
	flag.Parse()

	return &App{
		configPath: *configPath,
		checkChan:  make(chan struct{}),
		done:       make(chan struct{}),
		version:    version,
	}
}

// Initialize 初始化应用程序
func (app *App) Initialize() error {
	// 初始化配置文件路径
	if err := app.initConfigPath(); err != nil {
		return fmt.Errorf("初始化配置文件路径失败: %w", err)
	}

	// 加载配置文件
	if err := app.loadConfig(); err != nil {
		return fmt.Errorf("加载配置文件失败: %w", err)
	}

	// 初始化配置文件监听
	if err := app.initConfigWatcher(); err != nil {
		return fmt.Errorf("初始化配置文件监听失败: %w", err)
	}

	app.interval = config.GlobalConfig.CheckInterval

	if config.GlobalConfig.ListenPort != "" {
		if err := app.initHttpServer(); err != nil {
			return fmt.Errorf("初始化HTTP服务器失败: %w", err)
		}
	}

	if config.GlobalConfig.SubStorePort != "" {
		if runtime.GOOS == "linux" && runtime.GOARCH == "386" {
			slog.Warn("node不支持Linux 32位系统，不启动sub-store服务")
		}
		go assets.RunSubStoreService()
		// 求等吗得，日志会按预期顺序输出
		time.Sleep(500 * time.Millisecond)
	}

	// 启动内存监控
	monitor.StartMemoryMonitor()

	// 设置信号处理器
	utils.SetupSignalHandler(&check.ForceClose)
	return nil
}

// Run 运行应用程序主循环
func (app *App) Run() {
	defer func() {
		app.watcher.Close()
		if app.ticker != nil {
			app.ticker.Stop()
		}
		if app.cron != nil {
			app.cron.Stop()
		}
	}()

	slog.Info(fmt.Sprintf("进度展示: %v", config.GlobalConfig.PrintProgress))

	// 设置初始定时器模式
	app.setTimer()

	// 仅在cron表达式为空时，首次启动立即执行检测
	if config.GlobalConfig.CronExpression != "" {
		slog.Warn("使用cron表达式，首次启动不立即执行检测")
	} else {
		app.triggerCheck()
	}

	// 在主循环中处理手动触发
	for range app.checkChan {
		go app.triggerCheck()
	}
}

// setTimer 根据配置设置定时器
func (app *App) setTimer() {
	// 停止现有定时器
	if app.ticker != nil {
		// 应该先发送停止信号，防止被=nil后panic
		close(app.done)                // 发送停止信号
		app.done = make(chan struct{}) // 创建新通道
		app.ticker.Stop()
		app.ticker = nil
	}

	// 停止现有cron
	if app.cron != nil {
		app.cron.Stop()
		app.cron = nil
	}

	// 检查是否设置了cron表达式
	if config.GlobalConfig.CronExpression != "" {
		slog.Info(fmt.Sprintf("使用cron表达式: %s", config.GlobalConfig.CronExpression))
		app.cron = cron.New()
		_, err := app.cron.AddFunc(config.GlobalConfig.CronExpression, func() {
			app.triggerCheck()
		})
		if err != nil {
			slog.Error(fmt.Sprintf("cron表达式 '%s' 解析失败: %v，将使用检查间隔时间",
				config.GlobalConfig.CronExpression, err))
			// 使用间隔时间
			app.useIntervalTimer()
		} else {
			app.cron.Start()
		}
	} else {
		// 使用间隔时间
		app.useIntervalTimer()
	}
}

// useIntervalTimer 使用间隔时间模式运行
func (app *App) useIntervalTimer() {
	// 初始化定时器
	app.ticker = time.NewTicker(time.Duration(app.interval) * time.Minute)
	done := app.done
	// 启动一个goroutine监听定时器事件
	go func() {
		for {
			select {
			case <-app.ticker.C:
				app.triggerCheck()
			case <-done:
				return // 收到停止信号，退出goroutine
			}
		}
	}()
}

// TriggerCheck 供外部调用的触发检测方法
func (app *App) TriggerCheck() {
	select {
	case app.checkChan <- struct{}{}:
		slog.Info("手动触发检测")
	default:
		slog.Warn("已有检测正在进行，忽略本次触发")
	}
}

// triggerCheck 内部检测方法
func (app *App) triggerCheck() {
	// 如果已经在检测中，直接返回
	if !app.checking.CompareAndSwap(false, true) {
		slog.Warn("已有检测正在进行，跳过本次检测")
		return
	}
	defer app.checking.Store(false)

	if err := app.checkProxies(); err != nil {
		slog.Error(fmt.Sprintf("检测代理失败: %v", err))
		os.Exit(1)
	}

	// 检测完成后显示下次检查时间
	if app.ticker != nil {
		// 使用间隔时间模式
		app.ticker.Reset(time.Duration(app.interval) * time.Minute)
		nextCheck := time.Now().Add(time.Duration(app.interval) * time.Minute)
		slog.Info(fmt.Sprintf("下次检查时间: %s", nextCheck.Format("2006-01-02 15:04:05")))
	} else if app.cron != nil {
		// 使用cron模式
		entries := app.cron.Entries()
		if len(entries) > 0 {
			nextTime := entries[0].Next
			slog.Info(fmt.Sprintf("下次检查时间: %s", nextTime.Format("2006-01-02 15:04:05")))
		}
	}
	debug.FreeOSMemory()
}

// calculateMediaStats 计算流媒体解锁统计
func calculateMediaStats(results []check.Result) (int, int, int, int, int, int) {
	var netflixCount, youtubeCount, openaiCount, disneyCount, geminiCount, tiktokCount int

	for _, result := range results {
		if result.Netflix {
			netflixCount++
		}
		if result.Youtube != "" {
			youtubeCount++
		}
		if result.Openai {
			openaiCount++
		}
		if result.Disney {
			disneyCount++
		}
		if result.Gemini {
			geminiCount++
		}
		if result.TikTok != "" {
			tiktokCount++
		}
	}

	return netflixCount, youtubeCount, openaiCount, disneyCount, geminiCount, tiktokCount
}

// calculateAvgSpeed 计算平均速度
func calculateAvgSpeed(results []check.Result) float64 {
	if len(results) == 0 {
		return 0
	}

	totalSpeed := 0.0
	validCount := 0

	for _, result := range results {
		if result.Proxy != nil {
			if name, ok := result.Proxy["name"].(string); ok {
				// 从节点名称中提取速度信息 (格式如: "节点名|2.5MB/s")
				if strings.Contains(name, "MB/s") {
					parts := strings.Split(name, "|")
					for _, part := range parts {
						if strings.Contains(part, "MB/s") {
							speedStr := strings.TrimSpace(part)
							speedStr = strings.Replace(speedStr, "MB/s", "", 1)
							var speed float64
							if n, err := fmt.Sscanf(speedStr, "%f", &speed); err == nil && n == 1 {
								totalSpeed += speed
								validCount++
							}
						}
					}
				} else if strings.Contains(name, "KB/s") {
					parts := strings.Split(name, "|")
					for _, part := range parts {
						if strings.Contains(part, "KB/s") {
							speedStr := strings.TrimSpace(part)
							speedStr = strings.Replace(speedStr, "KB/s", "", 1)
							var speed float64
							if n, err := fmt.Sscanf(speedStr, "%f", &speed); err == nil && n == 1 {
								totalSpeed += speed / 1024 // 转换为MB/s
								validCount++
							}
						}
					}
				}
			}
		}
	}

	if validCount > 0 {
		return totalSpeed / float64(validCount)
	}
	return 0
}

// checkProxies 执行代理检测
func (app *App) checkProxies() error {
	slog.Info("开始检测代理")
	startTime := time.Now()

	results, err := check.Check()
	if err != nil {
		return fmt.Errorf("检测代理失败: %w", err)
	}

	endTime := time.Now()
	duration := endTime.Sub(startTime)

	// 将成功的节点添加到全局中，暂时内存保存
	if config.GlobalConfig.KeepSuccessProxies {
		for _, result := range results {
			if result.Proxy != nil {
				config.GlobalProxies = append(config.GlobalProxies, result.Proxy)
			}
		}
	}

	slog.Info("检测完成")
	save.SaveConfig(results)

	// 计算统计数据
	netflixCount, youtubeCount, openaiCount, disneyCount, geminiCount, tiktokCount := calculateMediaStats(results)
	avgSpeed := calculateAvgSpeed(results)

	// 发送增强通知
	notifyStats := utils.NotifyStats{
		TotalNodes:     int(check.ProxyCount.Load()),
		AvailableNodes: len(results),
		AvgSpeed:       avgSpeed,
		Duration:       duration,
		StartTime:      startTime,
		NetflixCount:   netflixCount,
		YouTubeCount:   youtubeCount,
		OpenAICount:    openaiCount,
		DisneyCount:    disneyCount,
		GeminiCount:    geminiCount,
		TikTokCount:    tiktokCount,
	}
	utils.SendEnhancedNotify(notifyStats)

	utils.UpdateSubs()

	// 执行回调脚本
	utils.ExecuteCallback(len(results))

	return nil
}

func TempLog() string {
	return filepath.Join(os.TempDir(), "subs-check.log")
}
